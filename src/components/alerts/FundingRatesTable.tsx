"use client";

import { useCoinglassData } from "@/lib/state";
import { ConfigProvider, Table, TableProps } from "antd";
import { useState } from "react";
import FullscreenWrapper from "../comman/FullscreenWrapper";

export const FundingRatesTable = () => {
  const { data: fundingData, isLoading, error } = useCoinglassData('funding-rate');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Define the exchanges we want to show (based on your data structure)
  const exchanges = ['Binance', 'OKX', 'Bybit', 'KuCoin', 'dYdX', 'Bitget', 'CoinEx', 'Bitfinex', 'Kraken', 'HTX', 'BingX', 'Gate'];

  // Transform data to match the funding rates table structure
  const tableData = fundingData?.map((coin: any) => {
    const rowData: any = {
      key: coin.symbol,
      symbol: coin.symbol,
    };

    // Add funding rates for each exchange from stablecoin_margin_list
    exchanges.forEach((exchange) => {
      const exchangeData = coin.stablecoin_margin_list?.find((item: any) => item.exchange === exchange);
      rowData[exchange.toLowerCase().replace('.', '_')] = exchangeData?.funding_rate || null;
    });

    return rowData;
  }) || [];

  const fundingRatesColumns: TableProps<any>["columns"] = [
    {
      title: "Symbol",
      dataIndex: "symbol",
      key: "symbol",
      width: 100,
      align: "left",
      fixed: 'left',
      render: (text) => (
        <div className="flex items-center gap-2">
          <span className="font-normal text-text-primary">{text || '-'}</span>
        </div>
      ),
    },
    ...exchanges.map((exchange) => ({
      title: (
        <div className="flex flex-col items-center">
          <span className="text-xs">{exchange}</span>
        </div>
      ),
      dataIndex: exchange.toLowerCase().replace('.', '_'),
      key: exchange.toLowerCase().replace('.', '_'),
      width: 90,
      align: "center" as const,
      render: (value: number) => {
        if (value === null || value === undefined) return '-';
        const percentage = value;
        const isPositive = percentage >= 0;
        const isNegative = percentage < 0;
        return (
          <span className={
            isPositive ? 'text-green-600' :
            isNegative ? 'text-red-600' :
            'text-gray-600'
          }>
            {percentage.toFixed(3)}%
          </span>
        );
      },
    })),
  ];

  if (error) {
    return (
      <div className="p-8">
        <p className="text-red-600">Error loading funding rates data</p>
      </div>
    );
  }

  return (
    <FullscreenWrapper title="Funding Rates Table">
      <section className="mr-4">
          <ConfigProvider
            theme={{
            components: {
              Spin: { colorPrimary: "#bbd955" },
              Table: {
                fontFamily: "DM Sans",
                colorPrimary: "#bbd955",
                headerBg: "#1a1a1a",
                headerColor: "#ffffff",
                rowHoverBg: "#2d3748",
                colorBgContainer: "#222831",
                colorText: "#ffffff",
                colorTextHeading: "#ffffff",
                borderColor: "#404040",
                colorBorder: "#404040",
                bodySortBg: "#222831",
              },
            },
            token: {
              colorBgContainer: "#222831",
              colorText: "#ffffff",
              colorTextHeading: "#ffffff",
              colorBorder: "#404040",
            },
          }}
          >
            <Table
              columns={fundingRatesColumns}
              dataSource={tableData}
              rowKey="key"
              loading={isLoading}
              scroll={{ x: "1200px" }}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size || 10);
                },
                showSizeChanger: true,
                showQuickJumper: false,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
              }}
              className="crypto-table-dark"
            />
          </ConfigProvider>
          <div className="pb-12" />
      </section>
    </FullscreenWrapper>
  );
};
