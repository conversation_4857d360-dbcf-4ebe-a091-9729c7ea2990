"use client";

import React, { useState, useRef, useEffect } from "react";
import { Maximize2, Minimize2 } from "lucide-react";

interface FullscreenWrapperProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
}

const FullscreenWrapper: React.FC<FullscreenWrapperProps> = ({
  children,
  title = "Chart",
  className = "",
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const toggleFullscreen = async () => {
    if (!document.fullscreenElement) {
      // Enter fullscreen
      try {
        if (containerRef.current?.requestFullscreen) {
          await containerRef.current.requestFullscreen();
        } else if ((containerRef.current as any)?.webkitRequestFullscreen) {
          await (containerRef.current as any).webkitRequestFullscreen();
        } else if ((containerRef.current as any)?.msRequestFullscreen) {
          await (containerRef.current as any).msRequestFullscreen();
        }
      } catch (error) {
        console.error("Error attempting to enable full-screen mode:", error);
      }
    } else {
      // Exit fullscreen
      try {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).msExitFullscreen) {
          await (document as any).msExitFullscreen();
        }
      } catch (error) {
        console.error("Error attempting to exit full-screen mode:", error);
      }
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!document.fullscreenElement;
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("msfullscreenchange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
      document.removeEventListener("msfullscreenchange", handleFullscreenChange);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      style={
        isFullscreen
          ? {
              width: "100%",
              height: "100%",
              backgroundColor: "#222831",
              padding: "0.5rem",
              display: "flex",
              flexDirection: "column",
              overflow: "hidden", // Prevent double scrollbars
            }
          : {}
      }
    >
      {/* Conditionally render based on fullscreen state */}
      {isFullscreen ? (
        <>
          {/* == Fullscreen Header == */}
          {/* This header contains the title and exit button, preventing overlap. */}
          <div className="flex items-center justify-between pb-2 pt-1 shrink-0">
            <h2 className="text-xl font-bold text-white">{title}</h2>
            <button
              onClick={toggleFullscreen}
              className="p-2 bg-gray-700/80 hover:bg-gray-600/80 text-white rounded-md transition-colors"
              title="Exit fullscreen"
            >
              <Minimize2 size={18} />
            </button>
          </div>

          {/* == Fullscreen Content == */}
          {/* `flex-1` makes content grow; `min-h-0` prevents overflow with tables/charts. */}
          <div className="flex-1 min-h-0 overflow-auto">{children}</div>
        </>
      ) : (
        <>
          {/* == Default Content == */}
          {children}

          {/* == Default Fullscreen Button == */}
          {/* Positioned absolutely on top of the default view. */}
          <button
            onClick={toggleFullscreen}
            className="absolute -top-10 right-2 z-100 p-2 bg-gray-800/80 hover:bg-gray-700/80 text-white rounded-md backdrop-blur-sm transition-colors"
            title="Enter fullscreen"
          >
            <Maximize2 size={16} />
          </button>
        </>
      )}
    </div>
  );
};

export default FullscreenWrapper;