"use client";

import React, { useMemo, useState } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears, subMonths } from 'date-fns';
import { formatChartDate, cn } from '@/lib/utils';
import { ToggleGroup, ToggleGroupItem } from '../ui/toggle-group';

import Loader from '../comman/Loader';
import FullscreenWrapper from '../comman/FullscreenWrapper';

const FederalFundsRateChart: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>("All");

  const timeRangeOptions = ["6m", "1y", "All"];

  const { startDate, endDate } = useMemo(() => {
    const now = new Date();
    const end = format(now, 'yyyy-MM-dd');
    let start: Date;

    switch (timeRange) {
      case '6m':
        start = subMonths(now, 6);
        break;
      case '1y':
        start = subYears(now, 1);
        break;
      case 'All':
      default:
        start = subYears(now, 40);
        break;
    }

    return {
      startDate: format(start, 'yyyy-MM-dd'),
      endDate: end
    };
  }, [timeRange]);

  // Fetch Federal Funds Rate data
  const fedFundsQuery = useFREDSeriesObservations('FEDFUNDS', startDate, endDate);

  // Fetch Bitcoin price data from FRED (CBBTCUSD)
  const btcQuery = useFREDSeriesObservations('CBBTCUSD', startDate, endDate);

  // Process and merge data for dual-axis chart
  const chartData = useMemo(() => {
    if (!fedFundsQuery.data?.observations) return [];

    const fedFundsData = fedFundsQuery.data.observations
      .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
      .map(obs => ({
        date: obs.date,
        fedFunds: parseFloat(obs.value),
      }));

    // Create Bitcoin data map (may be empty if no data)
    const btcMap = new Map();
    if (btcQuery.data?.observations) {
      btcQuery.data.observations
        .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
        .forEach(obs => {
          btcMap.set(obs.date, parseFloat(obs.value));
        });
    }

    // Merge data by date - include all Fed Funds data, Bitcoin where available
    const mergedData = fedFundsData
      .map(fedFundsItem => ({
        date: fedFundsItem.date,
        fedFunds: fedFundsItem.fedFunds,
        btc: btcMap.get(fedFundsItem.date) || null, // null if no Bitcoin data for this date
        formattedDate: formatChartDate(fedFundsItem.date, { showDay: true }),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return mergedData;
  }, [fedFundsQuery.data, btcQuery.data]);

  const formatFedFundsValue = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  const formatBTCValue = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value.toLocaleString()}`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-gray-800 p-3 border border-gray-600 rounded-lg shadow-lg">
          <p className="text-white font-medium">{`Date: ${formatChartDate(label, { showDay: true, showMonth: true, showYear: true })}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.name === 'Federal Funds Rate' ? formatFedFundsValue(entry.value) : formatBTCValue(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (fedFundsQuery.isLoading || btcQuery.isLoading) {
    return (
      <div className="w-full space-y-8 p-6">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full space-y-8 p-6">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Economic Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The FRED API integration is ready. Backend endpoints are being deployed to fetch economic indicators data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <FullscreenWrapper title="Federal Funds Rate vs Bitcoin Price">
      <div className="w-full space-y-8">
      <div className="space-y-8">
        <div className="w-full space-y-4">
          <div className="flex justify-end items-center">
            <div className="flex">
              <ToggleGroup
                type="single"
                value={timeRange}
                onValueChange={(value) => value && setTimeRange(value)}
                className="flex rounded overflow-hidden border border-border-light px-2 py-1 gap-2 text-white"
              >
                {timeRangeOptions.map((option) => (
                  <ToggleGroupItem
                    key={option}
                    value={option}
                    className={cn(
                      "outline-none border-none px-3 rounded last:rounded-tr last:rounded-br first:rounded-tl first:rounded-bl"
                    )}
                  >
                    {option}
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </div>
          </div>
          <div className="w-full">
            <div style={{ width: "100%", height: 500 }}>
              <ResponsiveContainer width="100%" height={500}>
                <LineChart
                  data={chartData}
                >
                  <CartesianGrid stroke="#5d5e5f" strokeDasharray="6 6" />
                  <XAxis
                    dataKey="date"
                    tick={{ fill: "#fff", fontSize: 10 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.getFullYear().toString();
                    }}
                    tickLine={false}
                    axisLine={false}
                    interval="preserveStartEnd"
                    minTickGap={40}
                  />
                  <YAxis
                    yAxisId="fedFunds"
                    orientation="left"
                    tick={{ fill: "#fff", fontSize: 10 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatFedFundsValue}
                    label={{
                      value: "Federal Funds Rate (%)",
                      angle: -90,
                      position: "insideLeft",
                      offset: 10,
                      style: { fill: "#3498DB", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <YAxis
                    yAxisId="btc"
                    orientation="right"
                    tick={{ fill: "#fff", fontSize: 10 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatBTCValue}
                    label={{
                      value: "Bitcoin Price (USD)",
                      angle: 90,
                      position: "insideRight",
                      offset: -5,
                      style: { fill: "#F7931A", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  {/* Federal Funds Rate Line */}
                  <Line
                    yAxisId="fedFunds"
                    type="monotone"
                    dataKey="fedFunds"
                    stroke="#3498DB"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#3498DB" }}
                    name="Federal Funds Rate"
                  />
                  {/* Bitcoin Line */}
                  <Line
                    yAxisId="btc"
                    type="monotone"
                    dataKey="btc"
                    stroke="#F7931A"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#F7931A" }}
                    name="Bitcoin Price"
                    connectNulls={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Information about Fed Funds Rate vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">Federal Funds Rate and Bitcoin</h3>
          <p className="text-gray-400 text-sm">
            The Federal Funds Rate is the interest rate at which banks lend to each other overnight.
            It&apos;s a primary tool of monetary policy. Low rates make borrowing cheaper and can drive
            investment into riskier assets like Bitcoin. High rates make traditional savings more
            attractive and can reduce demand for alternative investments. This rate directly influences
            the cost of capital and investment flows in the economy.
          </p>
        </div>
      </div>
    </div>
    </FullscreenWrapper>
  );
};

export default FederalFundsRateChart;
