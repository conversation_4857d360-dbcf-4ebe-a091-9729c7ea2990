@import "tailwindcss";
@import "tw-animate-css";

/* Fullscreen styles */
:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  background-color: #222831 !important;
}

:-webkit-full-screen {
  width: 100vw !important;
  height: 100vh !important;
  background-color: #222831 !important;
}

:-moz-full-screen {
  width: 100vw !important;
  height: 100vh !important;
  background-color: #222831 !important;
}

:-ms-fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  background-color: #222831 !important;
}

/* Ensure charts and tables expand in fullscreen */
:fullscreen .ant-table-wrapper,
:fullscreen .crypto-table {
  height: auto !important;
  max-height: calc(100vh - 120px) !important;
  width: 100% !important;
}

:fullscreen .ant-table-tbody {
  max-height: calc(100vh - 200px) !important;
  overflow-y: auto !important;
}

:fullscreen .recharts-wrapper,
:fullscreen .recharts-responsive-container,
:fullscreen div[style*="height"] {
  height: 85vh !important;
  min-height: 85vh !important;
  width: 100% !important;
}

:-webkit-full-screen .ant-table-wrapper,
:-webkit-full-screen .crypto-table {
  height: auto !important;
  max-height: calc(100vh - 120px) !important;
  width: 100% !important;
}

:-webkit-full-screen .ant-table-tbody {
  max-height: calc(100vh - 200px) !important;
  overflow-y: auto !important;
}

:-webkit-full-screen .recharts-wrapper,
:-webkit-full-screen .recharts-responsive-container,
:-webkit-full-screen div[style*="height"] {
  height: 85vh !important;
  min-height: 85vh !important;
  width: 100% !important;
}

:-moz-full-screen .ant-table-wrapper,
:-moz-full-screen .crypto-table {
  height: auto !important;
  max-height: calc(100vh - 120px) !important;
  width: 100% !important;
}

:-moz-full-screen .ant-table-tbody {
  max-height: calc(100vh - 200px) !important;
  overflow-y: auto !important;
}

:-moz-full-screen .recharts-wrapper,
:-moz-full-screen .recharts-responsive-container,
:-moz-full-screen div[style*="height"] {
  height: 85vh !important;
  min-height: 85vh !important;
  width: 100% !important;
}

:-ms-fullscreen .ant-table-wrapper,
:-ms-fullscreen .crypto-table {
  height: auto !important;
  max-height: calc(100vh - 120px) !important;
  width: 100% !important;
}

:-ms-fullscreen .ant-table-tbody {
  max-height: calc(100vh - 200px) !important;
  overflow-y: auto !important;
}

:-ms-fullscreen .recharts-wrapper,
:-ms-fullscreen .recharts-responsive-container,
:-ms-fullscreen div[style*="height"] {
  height: 85vh !important;
  min-height: 85vh !important;
  width: 100% !important;
}

/* Force full height for chart containers */
:fullscreen .recharts-responsive-container > div,
:-webkit-full-screen .recharts-responsive-container > div,
:-moz-full-screen .recharts-responsive-container > div,
:-ms-fullscreen .recharts-responsive-container > div {
  height: 100% !important;
}

/* Ensure table containers take full space and remove blank areas */
:fullscreen .ant-table,
:-webkit-full-screen .ant-table,
:-moz-full-screen .ant-table,
:-ms-fullscreen .ant-table {
  height: auto !important;
  margin: 0 !important;
}

/* Remove excessive padding and margins in fullscreen tables */
:fullscreen .ant-table-container,
:-webkit-full-screen .ant-table-container,
:-moz-full-screen .ant-table-container,
:-ms-fullscreen .ant-table-container {
  padding: 0 !important;
  margin: 0 !important;
}

/* Optimize table body height */
:fullscreen .ant-table-body,
:-webkit-full-screen .ant-table-body,
:-moz-full-screen .ant-table-body,
:-ms-fullscreen .ant-table-body {
  max-height: calc(100vh - 180px) !important;
  overflow-y: auto !important;
}

/* Remove extra spacing in table sections */
:fullscreen section,
:-webkit-full-screen section,
:-moz-full-screen section,
:-ms-fullscreen section {
  margin: 0 !important;
  padding: 0 !important;
}

/* Hide scrollbars in fullscreen for cleaner look */
:fullscreen::-webkit-scrollbar,
:-webkit-full-screen::-webkit-scrollbar {
  width: 8px;
}

:fullscreen::-webkit-scrollbar-track,
:-webkit-full-screen::-webkit-scrollbar-track {
  background: #2d3748;
}

:fullscreen::-webkit-scrollbar-thumb,
:-webkit-full-screen::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
}

@custom-variant dark (&:is(.dark *));

:root {
  font-family: var(--font-dm-sans), system-ui, Avenir, Helvetica, Arial,
    sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  isolation: isolate;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@theme {
  --color-bg-primary: #bbd955;
  --color-bg-secondary: #bbd95580;
  --color-bg-dark-white: #f7f7f7;

  --color-bg-black: #1d1d1d;
  --color-text-primary: #262626;
  --color-text-secondary: #767676;
  --color-border-light: #a2a9b880;

  --color-tag-green: #ddf2d5;
  --color-danger: #e03131;
  --breakpoint-tablet: 950px;
}

.no_css_styling {
  ul {
    list-style-type: disc;
    margin-left: 15px;
  }
  ol {
    list-style-type: decimal;
    margin-left: 15px;
  }
  ul ul,
  ol ul {
    list-style-type: circle;

    margin-left: 15px;
  }
  ol ol,
  ul ol {
    list-style-type: lower-latin;

    margin-left: 15px;
  }
  h1 {
    display: block;
    font-size: 2em;
    margin-top: 0.67em;
    margin-bottom: 0.67em;
    margin-left: 0;
    margin-right: 0;
    font-weight: bold;
  }
  h2 {
    display: block;
    font-size: 1.5em;
    margin-top: 0.83em;
    margin-bottom: 0.83em;
    margin-left: 0;
    margin-right: 0;
    font-weight: bold;
  }
  h3 {
    display: block;
    font-size: 1.17em;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    font-weight: bold;
  }
  h4 {
    display: block;
    font-size: 1em;
    margin-top: 1.33em;
    margin-bottom: 1.33em;
    margin-left: 0;
    margin-right: 0;
    font-weight: bold;
  }
  h5 {
    display: block;
    font-size: 0.83em;
    margin-top: 1.67em;
    margin-bottom: 1.67em;
    margin-left: 0;
    margin-right: 0;
    font-weight: bold;
  }
  h6 {
    display: block;
    font-size: 0.75em;
    margin-top: 1.67em;
    margin-bottom: 1.67em;
    margin-left: 0;
    margin-right: 0;
    font-weight: bold;
  }
}

.center {
  max-width: 1440px;
  margin: 0 auto;
  padding-left: 20px;
  padding-right: 20px;
}

/* Between 768px and 1440px */
@media (min-width: 768px) {
  .center {
    padding-left: 42px;
    padding-right: 42px;
  }
}

/* Below 768px */
@media (max-width: 767px) {
  .center {
    padding-left: 32px;
    padding-right: 32px;
  }
}

/* Below 768px */
@media (max-width: 400px) {
  .center {
    padding-left: 24px;
    padding-right: 24px;
  }
}

.profile_container {
  height: calc(100vh - 100px);
}

/* Default light theme table styling for home page */
.crypto-table .ant-table {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.crypto-table .ant-table-thead > tr > th {
  background-color: var(--color-bg-black) !important;
  color: white;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  text-align: center;
}

.ant-table-wrapper .ant-table-column-sorter {
  color: white !important;
}

.ant-table-wrapper .ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--color-border-light);
  &:first-of-type {
    border-left: 1px solid var(--color-border-light);
  }

  &:last-of-type {
    border-right: 1px solid var(--color-border-light);
  }
}

/* Dark theme table styling - only for specific dark tables */
.crypto-table-dark .ant-table {
  background-color: #222831 !important;
  border-radius: 8px;
  overflow: hidden;
}

/* Table header - darker background */
.crypto-table-dark .ant-table-thead > tr > th {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
  font-weight: 600;
  border-bottom: 2px solid #404040 !important;
  text-align: center;
}

/* Table body rows - match dark theme background */
.crypto-table-dark .ant-table-tbody > tr > td {
  background-color: #222831 !important;
  color: #ffffff !important;
  border-bottom: 1px solid #404040 !important;
}

/* Hover effect for table rows */
.crypto-table-dark .ant-table-tbody > tr:hover > td {
  background-color: #2d3748 !important;
  color: #ffffff !important;
}

/* Column sorter icons */
.crypto-table-dark .ant-table-column-sorter {
  color: #ffffff !important;
}

/* All text in table should be white */
.crypto-table-dark .ant-table-tbody > tr > td * {
  color: #ffffff !important;
}

/* Ensure links and spans are white */
.crypto-table-dark .ant-table-tbody > tr > td span,
.crypto-table-dark .ant-table-tbody > tr > td a {
  color: #ffffff !important;
}

/* Table container */
.crypto-table-dark .ant-table-container {
  background-color: #222831 !important;
}

/* Table wrapper */
.crypto-table-dark .ant-table-wrapper {
  background-color: transparent !important;
}

.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(
    .ant-table-row-expand-icon-cell
  ):not([colspan])::before,
:where(.css-dev-only-do-not-override-1jbbzgi).ant-table-wrapper
  .ant-table-thead
  > tr
  > td:not(:last-child):not(.ant-table-selection-column):not(
    .ant-table-row-expand-icon-cell
  ):not([colspan])::before {
  width: 0px;
}

.alerts-container {
  height: calc(100vh - 100px);
}
